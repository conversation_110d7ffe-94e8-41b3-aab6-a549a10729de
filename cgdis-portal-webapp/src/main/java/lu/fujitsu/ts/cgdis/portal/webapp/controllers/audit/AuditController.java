package lu.fujitsu.ts.cgdis.portal.webapp.controllers.audit;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.config.PermConfigDayType;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciRicRangeType;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerAssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerImportStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.security.AuditSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.audit.IAuditBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.utils.rql.RqlStringBooleanValueParser;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.core.utils.rql.parsers.RqlEnumValueParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * The type Service plan controller.
 */
@RestController
@RequestMapping("/audits")
@LogMilestone(category = LoggingCategory.REST_API_CALL, action = LoggingDomain.AUDIT)
public class AuditController {

  /**
   * The constant LOGGER.
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(AuditController.class);

  /**
   * The Rql parser.
   */
  private final SearchRqlParser rqlParser;
  /**
   * The Default sort field.
   */
  private final String defaultSortField;
  /**
   * The Default sort direction.
   */
  private final Sort.Direction defaultSortDirection;
  /**
   * The Default page size.
   */
  private final int defaultPageSize;
  /**
   * The Service.
   */
  @Autowired
  private IAuditBusinessService service;

  /**
   * Instantiates a new Audit controller.
   */
  public AuditController() {
    this.rqlParser = this.buildRqlParser();
    this.defaultSortField = "actionDatetime";
    this.defaultSortDirection = Sort.Direction.DESC;
    this.defaultPageSize = 10;
  }

  /**
   * Build rql parser search rql parser.
   *
   * @return the search rql parser
   */
  public SearchRqlParser buildRqlParser() {
    SearchRqlParser rqlParserTemp = new SearchRqlParser() {
    };
    rqlParserTemp.addCriterionParser("tecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("serviceplan", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("actionDatetime", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("actionDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("startDatetime", SearchRqlParser.RQL_LOCALDATETIME_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("endDatetime", SearchRqlParser.RQL_LOCALDATETIME_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("startDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("endDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("fromDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("targetDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("slotSplitMergeEndDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("slotSplitMergeEndDatetime", SearchRqlParser.RQL_LOCALDATETIME_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("fromDatetime", SearchRqlParser.RQL_LOCALDATETIME_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("targetDatetime", SearchRqlParser.RQL_LOCALDATETIME_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("deploymentPlanName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permServicePlanName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permServicePlanCategoryName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permServicePlanSubcategoryName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permServicePlanDeploymentPlanName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("type", new RqlEnumValueParser(AuditType.class));
    rqlParserTemp.addCriterionParser("actionType", new RqlEnumValueParser(AuditActionType.class));
    rqlParserTemp.addCriterionParser("permBookmarked", new RqlStringBooleanValueParser());
    rqlParserTemp.addCriterionParser("permConfigDpceEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCategoryName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceStartHour", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceDayValue", new RqlEnumValueParser(PermConfigDayType.class));
    rqlParserTemp.addCriterionParser("permDeploymentPlanStartDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);

    rqlParserTemp.addCriterionParser("permConfigDpceCopyEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyToEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyCategoryName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyStartHour", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyEndHour", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyToStartHour", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyToEndHour", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("permConfigDpceCopyDeploymentPlanName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("personTecid.cgdisRegistrationNumber", SearchRqlParser.RQL_STRING_VALUE_PARSER);

    rqlParserTemp.addCriterionParser("prestationStartdate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("prestationEnddate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("positionName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("prestationPersonTecid.cgdisRegistrationNumber", SearchRqlParser.RQL_STRING_VALUE_PARSER);

    // RICI SIM card audit parsers
    rqlParserTemp.addCriterionParser("riciSimCardTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciAssociatedPagerTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardIccid", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardMsisdn", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardStatus", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("associatedPagerId", SearchRqlParser.RQL_STRING_VALUE_PARSER); // Added parser for associatedPagerId

    // RICI RIC Range audit parsers
    rqlParserTemp.addCriterionParser("riciRicRangeTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciRicRangeName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciRicRangeType", new RqlEnumValueParser(RiciRicRangeType.class));
    rqlParserTemp.addCriterionParser("riciRicRangeEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);

    // RICI Pager audit parsers
    rqlParserTemp.addCriterionParser("riciPagerTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("pagerId", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("serialNumber", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerManufacturer", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerModel", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerDeliveryDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("status", new RqlEnumValueParser(RiciPagerStatus.class));
    rqlParserTemp.addCriterionParser("riciPagerAssociatedSimCardTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("associatedSimCardIccid", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("assignmentType", new RqlEnumValueParser(RiciPagerAssignmentType.class));
    rqlParserTemp.addCriterionParser("riciPagerAssignedPersonTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("assignedPersonTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("assignedEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerAssignedEntityName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("individualRic", SearchRqlParser.RQL_STRING_VALUE_PARSER);

    // RICI Schema audit parsers
    rqlParserTemp.addCriterionParser("riciSchemaTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaAlias", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaDescription", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeATecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeAName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeBTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeBName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeCTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeCName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeDTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSchemaFunctionCodeDName", SearchRqlParser.RQL_STRING_VALUE_PARSER);

    // New RICI SIM Card Import audit parsers
    rqlParserTemp.addCriterionParser("riciSimCardImportFileName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardImportStatus", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardImportTotalRecords", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardImportSuccessfulRecords", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardImportValidationErrors", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciSimCardImportImportErrors", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);

    // New RICI Pager Import audit parsers
    rqlParserTemp.addCriterionParser("riciPagerImportFileName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerImportStatus", new RqlEnumValueParser(RiciPagerImportStatus.class));
    rqlParserTemp.addCriterionParser("riciPagerImportTotalRecords", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerImportSuccessfulRecords", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerImportValidationErrors", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("riciPagerImportImportErrors", SearchRqlParser.RQL_INTEGER_VALUE_PARSER);

    // RICI Alert Group parsers
    rqlParserTemp.addCriterionParser("riciRicSchemaAlias", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParserTemp.addCriterionParser("alertGroupRiciRicRangeName", SearchRqlParser.RQL_STRING_VALUE_PARSER);

    return rqlParserTemp;
  }

  /**
   * Parse rql list.
   *
   * @param rql the rql
   * @return the list
   * @throws InvalidRQLExpressionException the invalid rql expression exception
   */
  private List<SearchCriterion> parseRql(String rql) throws InvalidRQLExpressionException {
    return this.rqlParser.parse(rql);
  }

  /**
   * Get security constraint icgdis security constraint.
   *
   * @return the icgdis security constraint
   */
  public IAuditSecurityConstraint getSecurityConstraint() {
    return new AuditSecurityConstraint();
  }

  /**
   * Prepares the PageRequest and processed SearchCriterion list from an RqlRequest.
   *
   * @param rqlRequest The RQL request containing pagination and search parameters.
   * @return A PreparedRqlRequest object containing the PageRequest and processed SearchCriterion list.
   * @throws InvalidRQLExpressionException if the RQL expression is invalid.
   */
  private PreparedRqlRequest prepareAuditSearchRequest(RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    PageRequest pageRequest = PageRequest.of(
      rqlRequest.getPage() == null ? 0 : rqlRequest.getPage(),
      rqlRequest.getPageSize() == null ? this.defaultPageSize : rqlRequest.getPageSize(),
      rqlRequest.getDirection() == null ? this.defaultSortDirection : rqlRequest.getDirection(),
      new String[]{StringUtils.isEmpty(rqlRequest.getOrderBy()) ? this.defaultSortField : rqlRequest.getOrderBy()}
    );
    List<SearchCriterion> rawCriterionList = this.parseRql(rqlRequest.getRql());
    List<SearchCriterion> finalCriterionList = processSearchCriteria(rawCriterionList);
    return new PreparedRqlRequest(pageRequest, finalCriterionList);
  }

  /**
   * Processes a list of raw search criteria, handling special cases like 'actionDatetime'.
   *
   * @param rawCriterionList The list of raw search criteria parsed from RQL.
   * @return A new list of processed search criteria.
   */
  private List<SearchCriterion> processSearchCriteria(List<SearchCriterion> rawCriterionList) {
    List<SearchCriterion> processedList = new ArrayList<>();
    for (SearchCriterion criterion : rawCriterionList) {
      if (criterion.getAttribute().equals("actionDatetime")) {
        LocalDate creationDate = (LocalDate) criterion.getValue();
        SearchCriterion from = new SearchCriterion("actionDatetime", creationDate.atStartOfDay(), SearchCriterion.Operator.ge);
        SearchCriterion to = new SearchCriterion("actionDatetime", creationDate.plusDays(1).atStartOfDay(), SearchCriterion.Operator.lt);
        processedList.add(from);
        processedList.add(to);
      } else {
        processedList.add(criterion);
      }
    }
    return processedList;
  }

  /**
   * Search page.
   *
   * @param rqlRequest the rql request
   * @return the page
   * @throws InvalidRQLExpressionException the invalid rql expression exception
   */
  @GetMapping(value = {"/model", ""})
  @LogMilestone(action = "search audit model", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<Audit> searchModels(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit Models");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.search(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL), Arrays.asList(AuditType.MODEL, AuditType.VERSION_MODEL), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  /**
   * Search page.
   *
   * @param rqlRequest the rql request
   * @return the page
   * @throws InvalidRQLExpressionException the invalid rql expression exception
   */
  @GetMapping(value = {"/serviceplan", ""})
  @LogMilestone(action = "search audit service plan", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<Audit> searchServicePlan(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit Service Plan");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.search(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN), Arrays.asList(AuditType.PDS, AuditType.VERSION_PDS), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  /**
   * Search page.
   *
   * @param rqlRequest the rql request
   * @return the page
   * @throws InvalidRQLExpressionException the invalid rql expression exception
   */
  @GetMapping(value = {"/prestations", ""})
  @LogMilestone(action = "search audit prestations", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditPrestation> searchPrestation(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit Prestations");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchPrestations(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/copyprestations", ""})
  @LogMilestone(action = "search audit copy prestations", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditCopyPrestation> searchCopyPrestation(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit copy Prestations");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchCopyPrestations(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/slots", ""})
  @LogMilestone(action = "search audit timeslots", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditTimeSlot> searchSlots(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit timeslots");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchTimeSlots(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/logas", ""})
  @LogMilestone(action = "search audit logas", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<Audit> searchLogas(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit Logas");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.search(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_LOGAS), Collections.singletonList(AuditType.LOGAS), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/allowance", ""})
  @LogMilestone(action = "search audit allowance", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<Audit> searchAllowance(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search audit Allowance");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.search(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE), Collections.singletonList(AuditType.ALLOWANCE_CONFIG), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/permamonitor/deploymentplans", ""})
  @LogMilestone(action = "search audit perm deployment plans", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditPermDeploymentPlan> searchPermDeploymentPlans(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search Perm Deployment Plans");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchPermDeploymentPlan(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/permamonitor/serviceplans", ""})
  @LogMilestone(action = "search audit perm service plans", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditPermServicePlanCategory> searchPermServicePlans(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search Perm Service Plans");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchPermServicePlanCategory(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/permamonitor/configurations/criticities", ""})
  @LogMilestone(action = "search audit perm config criticities", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditPermConfigDpce> searchPermConfigDpce(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchPermConfigDpce(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/permamonitor/configurations/criticitiescopy", ""})
  @LogMilestone(action = "search audit perm config criticities copy", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditPermConfigDpceCopy> searchPermConfigDpceCopy(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchPermConfigDpceCopy(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/rici/simcards", ""})
  @LogMilestone(action = "search audit rici sim cards", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditRiciSimCard> searchRiciSimCards(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI SIM Cards Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchRiciSimCards(this.getSecurityConstraint(), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/rici/ricranges", ""})
  @LogMilestone(action = "search audit rici ric ranges", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<Audit> searchRiciRicRanges(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI RIC Range Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.search(this.getSecurityConstraint(), Collections.singletonList(AuditType.RICI_RIC_RANGE), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = {"/rici/pagers", ""})
  @LogMilestone(action = "search audit rici pagers", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditRiciPager> searchRiciPagers(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI Pager Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchRiciPager(this.getSecurityConstraint(), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = "/rici/schemas")
  @LogMilestone(action = "search audit rici schemas", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<Audit> searchRiciSchemas(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI Schema Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.search(this.getSecurityConstraint(), Collections.singletonList(AuditType.RICI_SCHEMA), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  // New endpoint for RICI SIM Card Import Audits
  @GetMapping(value = "/rici/sim-card-imports")
  @LogMilestone(action = "search audit rici sim card imports", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditRiciSimCardImport> searchRiciSimCardImports(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI SIM Card Import Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchRiciSimCardImports(this.getSecurityConstraint(), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  // New endpoint for RICI Pager Import Audits
  @GetMapping(value = "/rici/pager-imports")
  @LogMilestone(action = "search audit rici pager imports", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditRiciPagerImport> searchRiciPagerImports(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI Pager Import Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchRiciPagerImports(this.getSecurityConstraint(), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = "/rici/alert-groups")
  @LogMilestone(action = "search audit rici schemas", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditRiciAlertGroup> searchRiciAlertGroups(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI Alert Groups Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchAlertGroup(new AuditSecurityConstraint(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  @GetMapping(value = "/rici/pager-alert-groups")
  @LogMilestone(action = "search audit rici pager alert groups", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<AuditRiciPagerAlertGroup> searchRiciPagerAlertGroups(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    LOGGER.debug("Search RICI Pager Alert Groups Audits");
    PreparedRqlRequest preparedRequest = prepareAuditSearchRequest(rqlRequest);
    return this.service.searchPagerAlertGroups(this.getSecurityConstraint(), Collections.singletonList(AuditType.RICI_PAGER_ALERT_GROUP), preparedRequest.getSearchCriteria(), preparedRequest.getPageRequest());
  }

  /**
   * Private helper class to hold prepared RQL request components.
   */
  private static class PreparedRqlRequest {
    private final PageRequest pageRequest;
    private final List<SearchCriterion> searchCriteria;

    public PreparedRqlRequest(PageRequest pageRequest, List<SearchCriterion> searchCriteria) {
      this.pageRequest = pageRequest;
      this.searchCriteria = searchCriteria;
    }

    public PageRequest getPageRequest() {
      return pageRequest;
    }

    public List<SearchCriterion> getSearchCriteria() {
      return searchCriteria;
    }
  }
}
