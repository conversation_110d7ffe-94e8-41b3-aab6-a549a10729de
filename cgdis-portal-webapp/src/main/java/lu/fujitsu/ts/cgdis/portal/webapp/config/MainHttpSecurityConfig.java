package lu.fujitsu.ts.cgdis.portal.webapp.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lu.fujitsu.ts.cgdis.portal.connector.ad.config.properties.AdConnectorProperties;
import lu.fujitsu.ts.cgdis.portal.connector.ad.mapper.IAuthorityMapper;
import lu.fujitsu.ts.cgdis.portal.core.config.properties.CoreProperties;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.webapp.properties.WebAppProperties;
import lu.fujitsu.ts.cgdis.portal.webapp.security.CGDISAuthenticationEntryPoint;
import lu.fujitsu.ts.cgdis.portal.webapp.security.handler.CGDISLogoutSuccessHandler;
import lu.fujitsu.ts.cgdis.portal.webapp.security.mapper.ICGDISUserDetailsContextMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.csrf.CsrfFilter;

//@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
//@Configuration
//@Order(4)
public abstract class MainHttpSecurityConfig extends WebSecurityConfigurerAdapter {

  @Autowired
  private ObjectMapper mapper;
  @Autowired
  private AdConnectorProperties properties;

  @Autowired
  private WebAppProperties webAppProperties;


  @Autowired
  private ICGDISUserDetailsContextMapper userDetailsContextMapper;

  @Autowired
  private IAuthorityMapper authorityMapper;

  @Autowired
  private CoreProperties coreProperties;


  @Override
  public void configure(WebSecurity security) {
    security.ignoring().antMatchers(
      "/check.html"
//      "/resource/**",
//      "/favicon.ico",
//      "/*.js",
//      "/*.css",
//      "/*.json",
//      "/*.woff2",
//      "/css/**/*",
//      "/maincss/**/*",
//      "/webjars/**",
//      "/webjars2/**",
//      "/webjars3/**",
//      "/"
    );

  }

  protected HttpSecurity executeCommonConfigure(HttpSecurity http, String baseApi) throws Exception {
    return http
      .authorizeRequests()
      .antMatchers(HttpMethod.GET, baseApi+ "api/rici/**/*").authenticated()
      .antMatchers(HttpMethod.PATCH, baseApi+ "api/rici/**/*").authenticated()
      .antMatchers(HttpMethod.DELETE, baseApi+ "api/rici/**/*").authenticated()
      .antMatchers(HttpMethod.POST, baseApi+ "api/rici/**/*").authenticated()
      .antMatchers(HttpMethod.PUT, baseApi+ "api/rici/**/*").authenticated()


      .antMatchers(HttpMethod.GET, baseApi+ "api/permamonitor/**/*").hasAnyAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR.name(), Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW.name())
      .antMatchers(HttpMethod.GET, baseApi+ "api/permamonitor/admin").hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW.name())
      .antMatchers(HttpMethod.GET, baseApi+ "api/permamonitor/admin/service-plans",baseApi+ "api/permamonitor/admin/service-plans").hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_ACCESS_CONFIG.name())
      .antMatchers(HttpMethod.PUT,  baseApi+ "api/permamonitor/admin/criticity/{deploymentPlanTecid}/{categoryName}/{entityTecid}",
                                    baseApi+ "api/permamonitor/admin/criticity/{deploymentPlanTecid}/{categoryName}/{entityTecid}/copy")
                                    .hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ.name())
      .antMatchers(HttpMethod.POST,
          baseApi+ "api/permamonitor/admin/criticity/{deploymentPlanTecid}/{categoryName}/{entityTecid}",
          baseApi+ "api/permamonitor/admin/criticity/{deploymentPlanTecid}/{categoryName}/{entityTecid}/copy/{toEntityTecid}"
        )
                                    .hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ.name())
      .antMatchers(HttpMethod.PATCH, baseApi+ "api/permamonitor/admin/deployment-plan/update").hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN.name())
      .antMatchers(HttpMethod.PATCH, baseApi+ "api/permamonitor/admin/*/service-plan/**/*").hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG.name())
      .antMatchers(HttpMethod.POST, baseApi+ "api/permamonitor/admin/*/service-plan/**/*").hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG.name())
      .antMatchers(HttpMethod.POST, baseApi+ "api/permamonitor/admin/deployment-plan/**/*").hasAuthority(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_CREATE_DEPLOYMENT_PLAN.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/mobileenrollment",baseApi+"api/mobileenrollment/**/*").hasAuthority(Permission.ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE.name())
      .antMatchers(HttpMethod.GET, baseApi+"api/news/**/*")
      .authenticated()
      .antMatchers(HttpMethod.GET, "/index.html","/","/static/index.html").authenticated()
      .antMatchers(HttpMethod.GET, baseApi+"api/login")
      .permitAll()
      .antMatchers(HttpMethod.POST, "/login", baseApi+"api/login")
      .permitAll()
      .antMatchers(HttpMethod.GET, baseApi+"api/general-availability/*")
      .hasAuthority(Permission.ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES.name())
      .antMatchers(baseApi+"api/actuator", baseApi+"api/error", "/error").permitAll()

      .antMatchers(HttpMethod.GET, baseApi+"api/i18n/**/*").authenticated()

      .antMatchers(HttpMethod.PUT, baseApi+"api/person-preference", baseApi+"api/person-preference/*", baseApi+"api/person-preference/**/*").authenticated()

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/",
        baseApi + "api/audits/_count",
        baseApi + "api/audits/{id:[0-9]+}"
      )
      .denyAll()

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/permamonitor/deploymentplans",
        baseApi + "api/audits/permamonitor/serviceplans",
        baseApi + "api/audits/permamonitor/configurations/criticities",
        baseApi + "api/audits/permamonitor/configurations/criticitiescopy"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR.name())

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/rici/simcards",
        baseApi + "api/audits/rici/sim-card-imports",
        baseApi + "api/audits/rici/ricranges",
        baseApi + "api/audits/rici/pagers",
        baseApi + "api/audits/rici/pager-imports",
        baseApi + "api/audits/rici/schemas",
        baseApi + "api/audits/rici/alert-groups",
        baseApi + "api/audits/rici/pager-alert-groups"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_RICI.name())

      .antMatchers(HttpMethod.GET,
       baseApi + "api/audits/prestations",
        baseApi + "api/audits/copyprestations",
        baseApi + "api/audits/slots"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS.name())

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/model"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL.name())

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/serviceplan"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN.name())

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/logas"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_LOGAS.name())

      .antMatchers(HttpMethod.GET,
        baseApi + "api/audits/allowance"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/security-role", baseApi+"api/security-role/*", baseApi+"api/security-role/**/*").hasAnyAuthority(Permission.ROLE_PERMISSION_USER_RIGHTS_DELETE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/security-role", baseApi+"api/security-role/*", baseApi+"api/security-role/**/*").hasAnyAuthority(Permission.ROLE_PERMISSION_USER_RIGHTS_UPDATE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/security-role", baseApi+"api/security-role/*", baseApi+"api/security-role/**/*").hasAnyAuthority(Permission.ROLE_PERMISSION_USER_RIGHTS.name(), Permission.ROLE_PERMISSION_USER_RIGHTS_VIEW.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/security-role/*").hasAnyAuthority(Permission.ROLE_PERMISSION_USER_RIGHTS_UPDATE.name())
      .antMatchers(HttpMethod.PUT, baseApi+"api/security-role/**").denyAll()

      .antMatchers(HttpMethod.GET, baseApi+"api/session/**/*").authenticated()

      .antMatchers(HttpMethod.GET, baseApi+"api/intervention/**/*").authenticated()

      .antMatchers(HttpMethod.GET, baseApi+"api/allowances/prestations/persons/{personTecid:[0-9]+}/date/{date:\\d{4}-\\d{2}-\\d{2}}",
        baseApi+"api/allowances/prestations/persons/{personTecid:[0-9]+}/info")
      .access("@logasAccessManager.canAccess(#personTecid,  {'ROLE_PERMISSION_ALLOWANCE_VIEW'},{'ROLE_PERMISSION_ALLOWANCE_VIEW'}, true )")

      .antMatchers(HttpMethod.GET, baseApi+"api/allowances/prestations", baseApi+"api/allowances/prestations-summary",baseApi+"api/allowances/prestations-summary/persons")
      .hasAuthority(Permission.ROLE_PERMISSION_ALLOWANCE_SEARCH.name())



            .antMatchers(HttpMethod.POST,baseApi+"api/allowances/prestations/totals")
      .hasAuthority(Permission.ROLE_PERMISSION_ALLOWANCE_SEARCH.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/allowances/prestations/{date:\\d{4}-\\d{2}-\\d{2}}/persons",
        baseApi+"api/allowances/prestations/{date:\\d{4}-\\d{2}-\\d{2}}",
        baseApi+"api/allowances/prestations/{date:\\d{4}-\\d{2}-\\d{2}}/entities",
        baseApi+"api/allowances/prestations/{date:\\d{4}-\\d{2}-\\d{2}}/{date:\\d{4}-\\d{2}-\\d{2}}/persons",
        baseApi+"api/allowances/prestations/{date:\\d{4}-\\d{2}-\\d{2}}/{date:\\d{4}-\\d{2}-\\d{2}}",
        baseApi+"api/allowances/prestations/{date:\\d{4}-\\d{2}-\\d{2}}/{date:\\d{4}-\\d{2}-\\d{2}}/entities")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_GENERATE_PRESTATIONS_ALLOWANCES.name())


      .antMatchers(HttpMethod.GET,
        baseApi+"api/allowances/prestations/generate/persons",
        baseApi+"api/allowances/prestations/generate/entities")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_GENERATE_PRESTATIONS_ALLOWANCES.name())


      .antMatchers(HttpMethod.GET, baseApi+"api/scheduler/exportBackup112")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_112_BACKUP_EXPORT.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/scheduler", baseApi+"api/scheduler/*", baseApi+"api/scheduler/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_SCHEDULER.name())


      .antMatchers(HttpMethod.GET, baseApi+"api/admin/vehicle/all")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_VEHICLES.name(),
        Permission.ROLE_PERMISSION_ADMIN_VEHICLES_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_VIEW.name()

      )
      .antMatchers(HttpMethod.GET, baseApi+"api/admin/vehicle/allWithBadStatus")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_VEHICLE_UPDATE_STATUS_6_VIEW.name()
      )

            .antMatchers(HttpMethod.POST, baseApi+"api/admin/vehicle/acknowledgeStatus/{statusId:[0-9]+}")
            .hasAnyAuthority(
                    Permission.ROLE_PERMISSION_VEHICLE_UPDATE_STATUS_6_VIEW.name()
            )

      .antMatchers(baseApi+"api/admin/vehicle/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_VEHICLES.name(),
        Permission.ROLE_PERMISSION_ADMIN_VEHICLES_VIEW.name()
      )

      .antMatchers(baseApi + "api/admin/box/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_BOX.name(),
        Permission.ROLE_PERMISSION_ADMIN_BOX_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_CREATE.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE.name()
      )




      .antMatchers(
        baseApi+"api/service-plan/state/{servicePlanId:[0-9]+}"
      ,  baseApi+"api/service-plan/state/entity/{entityId:[0-9]+}"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_PDS_STATE_VIEW.name())

      .antMatchers(baseApi+"api/service-plan-fill-view/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name())



      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/service-plan-model/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/service-plan-model/versions/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/service-plan-model/copy/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/service-plan-model/", baseApi+"api/admin/service-plan-model")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/service-plan-model/copyVersion/{versionId:[0-9]+}")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY.name())


      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/service-plan-model/versions", baseApi+"api/admin/service-plan-model/versions/")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/service-plan-model/minimum", baseApi+"api/admin/service-plan-model/*", baseApi+"api/admin/service-plan-model", baseApi+"api/admin/service-plan-model/")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/service-plan-model/versions/", baseApi+"api/admin/service-plan-model/versions", baseApi+"api/admin/service-plan-model/versions/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/service-plan-model/*/closure/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE.name())


      .antMatchers(HttpMethod.GET, baseApi+"api/admin/service-plan-model/versions/modelMaximumStartDateForServicePlan/{servicePlanId:[0-9]+}")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS.name()
      ,Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW.name()
      ,Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE.name()
      ,Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/service-plan-model/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS.name())

      .antMatchers(baseApi+"api/admin/service-plan-model/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/general-message/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE.name())

      .antMatchers(baseApi+"api/admin/position-template/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/position-template/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/position-template/versions/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE.name())


      .antMatchers(HttpMethod.POST, baseApi+"api/admin/position-template/", baseApi+"api/admin/position-template")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE.name())


      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/position-template/versions", baseApi+"api/admin/position-template/versions/")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/position-template/minimum", baseApi+"api/admin/position-template/*", baseApi+"api/admin/position-template", baseApi+"api/admin/position-template/")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/position-template/versions/", baseApi+"api/admin/position-template/versions", baseApi+"api/admin/position-template/versions/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/position-template/*/closure/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CLOSURE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/position-template/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/general-message/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/general-message/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/general-message/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE.name())


      .antMatchers(HttpMethod.GET, baseApi+"api/general-message/allForDateTime")
      .authenticated()



      .antMatchers(HttpMethod.PUT, "api/admin/function-operational/{id:[0-9]+}/validate")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/function-operational/{id:[0-9]+}/close")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/function-operational/listByEntityId/{entityId:[0-9]+}", baseApi+"api/admin/function-operational/summary", baseApi+"api/admin/function-operational/summaryCount"
        , baseApi+"api/admin/function-operational/personList"
      )
      .hasAnyAuthority(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/function-operational/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/function-operational/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/function-operational/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/function-operational/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/person-function-operational/byEntity/{entityId:[0-9]+}", baseApi+"api/person-function-operational/byEntity/{entityId:[0-9]+}/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name())

      .antMatchers(HttpMethod.GET, baseApi + "api/person-function-operational/byPerson/{personId:[0-9]+}", baseApi + "api/person-function-operational/byEntity/{entityId:[0-9]+}/*")
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL'" +
        "}, false) ")



      .antMatchers(HttpMethod.PUT, baseApi+"api/person-function-operational/mapByEntity/{functionOperationalId:[0-9]+}/{assignmentId:[0-9]+}", baseApi+"api/person-function-operational/mapByEntity/{functionOperationalId:[0-9]+}/{assignmentId:[0-9]+}/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE.name()
      )




      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/public-holiday/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/public-holiday/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/public-holiday/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/public-holiday/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY.name())

      .antMatchers(HttpMethod.GET,
        baseApi+"api/current-situation/entities",
        baseApi+"api/current-situation/schedules/{entityId:[0-9]+}",
              baseApi+"api/current-situation/refreshVehicleStatus"
      )
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_LIST_VIEW.name(),
        Permission.ROLE_PERMISSION_CURRENT_SITUATION.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/entity_category/**/*", baseApi+"api/entity_category")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES_VIEW.name())

      .antMatchers(HttpMethod.GET,  baseApi+"api/entities/{entityId:[0-9]+}")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES_VIEW.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/entities/foruserpermissions")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+baseApi+"api/entities/foruserpermissionsandtype")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES_VIEW.name()
      )

      .antMatchers(HttpMethod.GET,  baseApi+"api/entities/**/*", baseApi+"api/entities")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES_VIEW.name())



      .antMatchers(HttpMethod.PUT, baseApi+"api/entities-with-parent/*", baseApi+"api/entities-with-parent", baseApi+"api/entities-with-parent/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/drivinglicenseinformation/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'drivinglicence',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET, baseApi+"api/allowance/details")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW.name())



      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/allowance-configuration/{versionId}/{tecid:[0-9+]")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_DELETE.name())

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/allowance-configuration/{versionId}",baseApi+"api/admin/allowance-configuration/{versionId}/")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_CREATE.name())

      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/allowance-configuration/{versionId}/{tecid:[0-9+]",
        baseApi+"api/admin/allowance-configuration/{versionId}/{tecid:[0-9+]/minimal")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_UPDATE.name())

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/allowance-configuration",baseApi+"api/admin/allowance-configuration/",baseApi+"api/admin/allowance-configuration/{tecid:[0-9]+}")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_VIEW.name())






//      .antMatchers(HttpMethod.GET, baseApi+"api/allowance-configuration/configuration")
//      .hasAnyAuthority(Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW.name())
//      .antMatchers(HttpMethod.DELETE, "baseApi+/api/allowance-configuration/**/*")
//      .hasAnyAuthority(Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW.name())
//      .antMatchers(HttpMethod.DELETE, baseApi+"api/allowance-configuration/**/*")
//      .hasAnyAuthority(Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW.name())
       .antMatchers(HttpMethod.GET, baseApi+"api/diplomas/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'person-diplomas',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS'" +
        "}, false) ")

      .antMatchers(HttpMethod.GET, baseApi+"api/generalcontact/**/*")
      .denyAll()

      .antMatchers(HttpMethod.DELETE, baseApi+"api/generalcontact/*").denyAll()

      .antMatchers(HttpMethod.POST, baseApi+"api/generalcontact", baseApi+"api/generalcontact/")
//      .hasAnyAuthority(Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE.name())
      .denyAll()
      .antMatchers(HttpMethod.PUT, baseApi+"api/generalcontact", baseApi+"api/generalcontact/", baseApi+"api/generalcontact")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE.name()
      )
//      .denyAll()
      .antMatchers(HttpMethod.GET, baseApi+"api/interventiontypes", baseApi+"api/interventiontypes/**/*", baseApi+"api/interventiontypes/availability/{personId:[0-9]+}").authenticated()

      .antMatchers(HttpMethod.GET,
        baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}/entities",
        baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}",
        baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}/all"

      )
      .access("@logasAccessManager.canAccess(#personTecid,  {},{'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW'}, true )")

      .antMatchers(HttpMethod.POST, baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}/copy", baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}/copyWeek")
      .access("@logasAccessManager.canAccess(#personTecid,  {},{'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY'}, true) ")
      .antMatchers(HttpMethod.POST, baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}")
      .access("@logasAccessManager.canAccess(#personTecid,  {},{'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE'}, true) ")

      .antMatchers(HttpMethod.DELETE, baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}/{availabilityTecid:[0-9]+}")
      .access("@logasAccessManager.canAccess(#personTecid,  {},{'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE'}, true) ")

      .antMatchers(HttpMethod.PUT, baseApi+"api/volunteerAvailabilities/logas/{personTecid:[0-9]+}")
      .access("@logasAccessManager.canAccess(#personTecid,  {},{'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE'}, true) ")

      .antMatchers(HttpMethod.GET, baseApi+"api/volunteerAvailabilities/all-information")
      .access("@logasAccessManager.canAccessPersonRql(#this,'volunteerAvailabilities',  {" +
        "'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW'},{" +
        "'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET, baseApi+"api/volunteerAvailabilities/**/*",baseApi+"api/volunteerAvailabilities")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_.name(), Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW.name())

      .antMatchers(HttpMethod.DELETE, baseApi+"api/volunteerAvailabilities/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE.name())


      .antMatchers(HttpMethod.POST, baseApi+"api/volunteerAvailabilities/copy", baseApi+"api/volunteerAvailabilities/copyWeek")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY.name())


      .antMatchers(HttpMethod.POST, baseApi+"api/volunteerAvailabilities/", baseApi+"api/volunteerAvailabilities")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE.name())


      .antMatchers(HttpMethod.PUT, baseApi+"api/volunteerAvailabilities", baseApi+"api/volunteerAvailabilities/", baseApi+"api/volunteerAvailabilities/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE.name())

      .antMatchers(HttpMethod.GET,        baseApi+"api/suspensioninformation/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'suspensioninformation',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,        baseApi+"api/person-operational-grade/all"      )
      .access("@logasAccessManager.canAccessPersonRql(#this,'person-operational-grade',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,        baseApi+"api/person-operational-dates/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'person-operational-dates',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,        baseApi+"api/person-managerial-occupation/all"      )
      .access("@logasAccessManager.canAccessPersonRql(#this,'person-managerial-occupation',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,        baseApi+"api/person-operational-occupation/all"      )
      .access("@logasAccessManager.canAccessPersonRql(#this,'person-operational-occupation',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person-restriction/all-descriptions",
        baseApi+"api/person-restriction/all-types",
        baseApi+"api/person-aptitude/all-aptitude",
        baseApi+"api/person-aptitude/all-status"
      )
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS.name()
      )
      .antMatchers(HttpMethod.GET,        baseApi+"api/person-restriction/all"      ).access("@logasAccessManager.canAccessPersonRql(#this,'person-restriction',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,        baseApi+"api/person-aptitude/all"      ).access("@logasAccessManager.canAccessPersonRql(#this,'person-aptitude',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW'" +
        "}, false) ")

      .antMatchers(HttpMethod.GET,
        baseApi+"api/person-restriction/all-descriptions",
        baseApi+"api/person-restriction/all-types",
        baseApi+"api/person-aptitude/all-aptitude",
        baseApi+"api/person-aptitude/all-status"
      )
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS.name()
      )
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person-medical-remark/all/{personId}",
        baseApi+"api/person-medical-report/all/{personId:[0-9]+}"
      )
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET, baseApi+"api/personoperationalfunctions/**/*")
//      .hasAnyAuthority(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT.name(), Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name())
      .denyAll()
      .antMatchers(HttpMethod.GET,        baseApi+"api/person/assignment/operationalfunction"      )
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name()
      )
      .antMatchers(HttpMethod.GET,        baseApi+"api/person/assignment/logas"      )
      .hasAnyAuthority(        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name()      )

      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/assignment/logas/medical-information",
        baseApi+"api/person-aptitude/all-aptitude-details",
        baseApi+"api/person-restriction/all-restrictions"
      )
      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST.name()
      )
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/functions/{personId:[0-9]+}/{entityId:[0-9]+}"
      )
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name(),
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name()
//      )
      .access("@logasAccessManager.canAccess(#personId, {},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW','ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/generalcontactinformation/{personId:[0-9]+}"
      )
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW'" +
        ",'ROLE_PERMISSION_CURRENT_SITUATION'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/generalinformation/{personId:[0-9]+}",
        baseApi+"api/person-aptitude/{personId:[0-9]+}/checkexpiredaptitudes",
        baseApi+"api/person-aptitude/{personId:[0-9]+}/isimported"
      )
      .access("@logasAccessManager.canAccess( #personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW'" +
        ",'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW'" +
        ",'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/adresses/{personId:[0-9]+}"
      )
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW'}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/roles"
      )
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW.name(),
        Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW.name()
      )
     .antMatchers(HttpMethod.GET,
        baseApi+"api/person/roles/logas"
     )
      .access("@logasAccessManager.canAccessPersonRql(#this,'person',  {},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE'" +
        "}, false) ")
      .antMatchers(baseApi+"api/person/existsinternship/{personId:[0-9]+}")
      .access("@logasAccessManager.canAccess(#personId, {'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/operationalcontactinformation/{personId:[0-9]+}",
        baseApi+"api/person/operationalvolunteerinternship/{personId:[0-9]+}",
        baseApi+"api/person/existsinternship/{personId:[0-9]+}",
        baseApi+"api/person/operationalyoungfirefighter/{personId:[0-9]+}",
        baseApi+"api/person/bankaccount/{personId:[0-9]+}"
      )
      .access("@logasAccessManager.canAccess(#personId, {'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/person/bankaccount/{personId:[0-9]+}"
      )
      .access("@logasAccessManager.canAccess(#personId, " +
       "{'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW'}," +
        "{'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW'}, false) ")
      .antMatchers(HttpMethod.GET, baseApi+"api/configuration/front")
      .authenticated()

      .antMatchers(HttpMethod.POST, baseApi+"api/configuration/backupMode")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_112_BACKUP_ACTIVATE.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/configuration/version")
      .permitAll()

      .antMatchers(HttpMethod.GET, baseApi+"api/person/functions**/*")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT.name(),
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name()
//      )
      .denyAll()
      .antMatchers(HttpMethod.GET, baseApi+"api/optional-backup-group/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_CREATE.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name()
      )


      .antMatchers(HttpMethod.GET, baseApi+"api/optional-backup-group/all",baseApi+"api/optional-backup-group/summary", baseApi+"api/optional-backup-group-person/all")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW.name()
      )



      .antMatchers(HttpMethod.PUT, baseApi+"api/operationalcontact/", baseApi+"api/operationalcontact")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/operationalcontact/**/*").denyAll()

      .antMatchers(HttpMethod.DELETE, baseApi+"api/operationalcontact/**/*").denyAll()

      .antMatchers(HttpMethod.GET, baseApi+"api/operationalfunction/all")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION.name()
      )

      .antMatchers(HttpMethod.PUT, baseApi+"api/operationalfunction/**/*")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE.name()
//      )
      .denyAll()
      .antMatchers(HttpMethod.POST, baseApi+"api/operationalfunction/**/*").denyAll()

      .antMatchers(HttpMethod.DELETE, baseApi+"api/operationalfunction/**/*").denyAll()

      .antMatchers(HttpMethod.GET, baseApi+"api/availabilities/byEntity")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_CURRENT_SITUATION.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/availabilities/switch/{personId:[0-9]+}")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_CURRENT_SITUATION.name(),
//        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
//        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
//      )
      .access("@logasAccessManager.canAccess(#personId, {},{" +
        "'ROLE_PERMISSION_PDS_FILL_ADD_PERSON'}, false) ")


      .antMatchers(HttpMethod.GET, baseApi+"api/availabilities/serviceplans/{servicePlanId:[0-9]+}/positions/{positionId:[0-9]+}")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE.name()
      )


      .antMatchers(HttpMethod.GET, baseApi+"api/availabilities", baseApi+"api/availabilities/")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/person/entity/{entityId:[0-9]+}")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
      )

      .antMatchers(HttpMethod.GET,        baseApi+"api/assignmentfunction/personoperationalfunctions/{personOperationalFunctionId:[0-9+]}")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW.name(),
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW.name()
//      )
      .denyAll()
      .antMatchers(HttpMethod.GET,        baseApi+"api/activeassignmentsinformation/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'activeassignmentsinformation',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/activeassignmentsinformation/onlyexternal/{personId:[0-9]+}",
        baseApi+"api/suspensioninformation/{personId:[0-9]+}/issuspended")
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW','ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW'}, false) ")

      .antMatchers(HttpMethod.PUT, baseApi+"api/assignmentfunction", baseApi+"api/assignmentfunction/")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE.name(),
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE.name()
//      )
      .denyAll()

      .antMatchers(HttpMethod.POST, baseApi+"api/assignmentfunction", baseApi+"api/assignmentfunction/")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE.name()
      )

      .antMatchers(HttpMethod.DELETE, baseApi+"api/activeassignmentsinformation/**/*", baseApi+"api/assignmentfunction/**/*")
      .denyAll()

      .antMatchers(HttpMethod.PUT, baseApi+"api/activeassignmentsinformation/**/*", baseApi+"api/assignmentfunction/**/*")
      .denyAll()

      .antMatchers(HttpMethod.GET, baseApi+"api/serviceplantype/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/prestationssummaries/{personId:[0-9]+}", baseApi+"api/activeassignmentsinformation/allAssignement/{personId:[0-9]+}")
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW'}, false) ")
      .antMatchers(HttpMethod.GET,  baseApi+"api/activeassignmentsinformation/allAssignementStatusForEntity")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name(), Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW.name()
      )
      .antMatchers(HttpMethod.GET,  baseApi+"api/activeassignmentsinformation/existtechnical/{entityId:[0-9]+}")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/availabilitiessummary/{personId:[0-9]+}")
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW'}, false) ")
      .antMatchers(HttpMethod.GET, baseApi+"api/totals/summary")
      .access("@logasAccessManager.canAccessPersonQuery(#this, 'personId',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW'" +
        "}, false) ")
      .antMatchers(HttpMethod.GET, baseApi+"api/prestations/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'prestations',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW','ROLE_PERMISSION_PDS_FILL_VIEW','ROLE_PERMISSION_PDS_FILL_ADD_PERSON'" +
        "}, false) ")

      .antMatchers(HttpMethod.GET, baseApi+"api/prestations/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_VIEW.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name(),
        Permission.ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION.name()
      )


      .antMatchers(HttpMethod.DELETE, baseApi+"api/prestations/*", baseApi+"api/prestations/deleteWithAvailability/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/prestations", baseApi+"api/prestations/")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
      )

      .antMatchers(HttpMethod.PUT, baseApi+"api/prestations/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
      )

      .antMatchers(HttpMethod.DELETE, baseApi+"api/admin/service-plan/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_DELETE.name()
      )

      .antMatchers(HttpMethod.DELETE, baseApi+"api/serviceplantimeslot/*", baseApi+"api/serviceplanversion/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/serviceplanversion/copyVersion/{versionId:[0-9]+}")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/service-plan/manualSynchroEls/{servicePlanTecid:[0-9]+}")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/planning/logas/**")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/planning", baseApi+"api/planning/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_DASHBOARD_MEMBER.name(),
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/admin/service-plan", baseApi+"api/admin/service-plan/", baseApi+"api/admin/service-plan/*/assign")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_CREATE.name()
      )

      .antMatchers(HttpMethod.PUT,
        baseApi+"api/admin/service-plan/minimum")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE.name()
      )

      .antMatchers(HttpMethod.PUT,
        baseApi+"api/admin/service-plan/vehicle")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VEHICLE_UPDATE.name()
      )

      .antMatchers(HttpMethod.PUT,
        baseApi+"api/admin/service-plan/zone")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL.name()
      )

      .antMatchers(HttpMethod.PUT,
        baseApi+"api/admin/service-plan/national")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/service-plan/*/assign")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()
      )
      .antMatchers(HttpMethod.PUT, baseApi+"api/service-plan/*/assignTeam")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_TEAM.name()
      )

      .antMatchers(HttpMethod.POST, baseApi+"api/service-plan/*/assignfullavailability")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY.name()
      )
      .antMatchers(HttpMethod.GET,
      baseApi+"api/admin/service-plan/{servicePlanId:[0-9]+}/is-optional-backup-group")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE.name()
        ,Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL.name()
        ,Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL.name()
      )
      .antMatchers(HttpMethod.POST,
        baseApi+"api/serviceplantimeslot",
        baseApi+"api/serviceplantimeslot",
        baseApi+"api/serviceplanversion",
        baseApi+"api/serviceplanversion/")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE.name()
      )

      .antMatchers(HttpMethod.POST,
        baseApi+"api/serviceplantimeslot/{timeSlotTecid:[0-9]+}/split",
        baseApi+"api/serviceplantimeslot/{timeSlotTecid:[0-9]+}/merge"
      )
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT.name()
      )


      .antMatchers(HttpMethod.PUT, baseApi+"api/admin/service-plan/{servicePlanId:[0-9]+}/closure/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_UPDATE.name()
      )

      .antMatchers(HttpMethod.PUT,
        baseApi+"api/serviceplantimeslot",
        baseApi+"api/serviceplantimeslot/",
        baseApi+"api/serviceplanversion/**/*",
        baseApi+"api/serviceplanversion")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/export/service-plan/*/csv")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_EXPORT.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/export/persons/*/csv")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/export/persons/prestation/{personId:[0-9]+}/csv", baseApi+"api/export/persons/prestation-detail/{personId:[0-9]+}/csv")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS.name(),
//        Permission.ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS.name()
//      )
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS','ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS'}, false) ")



      .antMatchers(HttpMethod.GET, baseApi+"api/export/persons/prestation-penalty/{personId:[0-9]+}/csv")
//      .hasAnyAuthority(
//        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS.name(),
//        Permission.ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS.name()
//      )
      .access("@logasAccessManager.canAccess(#personId, {" +
        "'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY','ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY'}, false) ")

      .antMatchers(HttpMethod.GET, baseApi+"api/export/persons/prestationForEntity/*/csv")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/export/service-plan/*/pdf")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_EXPORT_PDF.name()
      )
      .antMatchers(HttpMethod.PUT, baseApi+"api/export/service-plan/{servicePlanId:[0-9]+}/sendFilling")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_SEND_FILLING.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/service-plan/{servicePlanId:[0-9]+}/teams")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_PDS_FILL_ADD_PERSON.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/service-plan/all")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VIEW.name(),
        Permission.ROLE_PERMISSION_PDS_LIST.name(),
        Permission.ROLE_PERMISSION_PDS_LIST_VIEW.name(),
        Permission.ROLE_PERMISSION_DASHBOARD_MEMBER.name(),
        Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS.name(),
        Permission.ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/service-plan/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VIEW.name(),
        Permission.ROLE_PERMISSION_PDS_LIST.name(),
        Permission.ROLE_PERMISSION_PDS_LIST_VIEW.name(),
        Permission.ROLE_PERMISSION_DASHBOARD_MEMBER.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/service-plan/{modelId:[0-9]+}/checkVehicleTypes")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE.name()
      )

      .antMatchers(HttpMethod.GET,
        baseApi+"api/admin/service-plan/{servicePlanId:[0-9]+}/teams",
        baseApi+"api/admin/service-plan/{servicePlanId:[0-9]+}/teams/")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW.name()

      )
      .antMatchers(HttpMethod.GET,
        baseApi+"api/admin/service-plan/{servicePlanId:[0-9]+}/positions")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/admin/service-plan/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VIEW.name()

      )
      .antMatchers(HttpMethod.GET, baseApi+"api/admin/service-plan/*/is-closed")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/totals/all")
      .access("@logasAccessManager.canAccessPersonRql(#this,'totals',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW'" +
        "}, true) ")
      .antMatchers(HttpMethod.GET,  baseApi+"api/totals/prestations")
      .access("@logasAccessManager.canAccessPersonQuery(#this, 'personId',  {" +
        "'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW'},{" +
        "'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW'" +
        "}, true) ")
      .antMatchers(HttpMethod.GET,
        baseApi+"api/serviceplantimeslot",
        baseApi+"api/serviceplantimeslot/",
        baseApi+"api/serviceplantimeslot/{timeSlotId:[0-9]+}",
        baseApi+"api/serviceplantimeslot/servicePlanVersion/{servicePlanVersionId:[0-9]+}",
        baseApi+"api/serviceplanversion/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE.name(),
        Permission.ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/dashboard/planning/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_DASHBOARD_MEMBER.name()

      )

      .antMatchers(HttpMethod.GET, baseApi+"api/error-management", baseApi+"api/error-management/*", baseApi+"api/error-management/**/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ERROR_MANAGEMENT.name()
      )

      .antMatchers(HttpMethod.PUT, baseApi+"api/error-management/*", baseApi+"api/error-management/*/resend")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ERROR_MANAGEMENT.name()
      )


      .antMatchers(HttpMethod.DELETE, baseApi+"api/error-management/*")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_ERROR_MANAGEMENT.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/logas/volunteeravailabilities")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/logas/{personId:[0-9]+}/access/medical") // PersonId checked in the controller
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/logas/{personId:[0-9]+}/access/diploma") // PersonId checked in the controller
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS.name(),
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS.name()
      )

      .antMatchers(HttpMethod.GET, baseApi+"api/logas/{personId:[0-9]+}/access/operational") // PersonId checked in the controller
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW.name(),
        Permission.ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW.name()
      )
      .antMatchers(HttpMethod.GET, baseApi+"api/logas/{personId:[0-9]+}/access/activity") // PersonId checked in the controller
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW.name(),
        Permission.ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW.name()
      )
      .antMatchers(baseApi+"api/admin/**/*")
      .hasAnyAuthority(Permission.ROLE_PERMISSION_ADMIN.name())

      .antMatchers(HttpMethod.GET, baseApi + "api/impersonate/persons")
      .hasAnyAuthority(
        Permission.ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS.name()
      )
      .antMatchers(baseApi+"api/**/*").denyAll()
      .and()
      .exceptionHandling().authenticationEntryPoint(authenticationEntryPoint())
      .and()
      .logout().logoutSuccessHandler(logoutHandler())
      .logoutUrl(baseApi+"api/logout")
      .deleteCookies("JSESSIONID")
      .invalidateHttpSession(true)
      .permitAll()
      .and()
      .addFilterAfter(new CGDISAuthenticationEntryPoint.CsrfHeaderFilter(), CsrfFilter.class)
      .csrf().disable();
  }


  //  @Bean
//  public CGDISAuthenticationEntryPoint.RequestBodyAuthenticationFilter authenticationFilter() throws Exception {
//    CGDISAuthenticationEntryPoint.RequestBodyAuthenticationFilter authenticationFilter = new CGDISAuthenticationEntryPoint.RequestBodyAuthenticationFilter(mapper);
//    authenticationFilter.setAuthenticationSuccessHandler(new AuthenticationSuccessHandler() {
//      @Override
//      public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
//        //LOGGER.info("user {} successfully authenticated", authentication.getPrincipal());
//
//      }
//    });
//    authenticationFilter.setAuthenticationFailureHandler(authenticationFailureHandler());
//    authenticationFilter.setRequiresAuthenticationRequestMatcher(new AntPathRequestMatcher(baseApi+"api/login", "POST"));
//    authenticationFilter.setAuthenticationManager(authenticationManagerBean());
//    return authenticationFilter;
//  }
//
//  private AuthenticationFailureHandler authenticationFailureHandler() {
//    return new SimpleUrlAuthenticationFailureHandler();
//  }
//
//  private AuthenticationSuccessHandler authenticationSuccessHandler() {
//    return new CGDISAuthenticationSuccessHandler();
//  }
//
//
  private AuthenticationEntryPoint authenticationEntryPoint() {
    return new CGDISAuthenticationEntryPoint(webAppProperties.getLoginUrl());
  }

  //
//
  private LogoutSuccessHandler logoutHandler() {
    return new CGDISLogoutSuccessHandler();
  }


}
