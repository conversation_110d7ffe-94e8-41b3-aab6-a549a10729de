<div class="row">
  <div *ngIf="!audit; else auditInformation">
    <!-- Placeholder or loading indicator -->
  </div>
  <ng-template #auditInformation>
    <div class="col-sm-12">
      <table>
        <!-- Common Audit Fields (Mobile Only) -->
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>

        <!-- Pager Alert Group Specific fields -->
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.person.name' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personName | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.person.cgdis.registration.number' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personCgdisRegistrationNumber | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.alert.group.name' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.alertGroupName | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.schema.alias' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.riciRicSchemaAlias | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.range.name' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.riciRicRangeName | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.pager.id' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.pagerPagerId | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.pager.serial.number' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.pagerSerialNumber | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.pager.alert.group.action' | translate }}</span>
          </td>
          <td class="list-value">
            <span class="badge" [ngClass]="audit.alertGroupValue ? 'badge-success' : 'badge-danger'">
              {{ (audit.alertGroupValue ? 'audit.rici.pager.alert.group.assigned' : 'audit.rici.pager.alert.group.removed') | translate }}
            </span>
          </td>
        </tr>
      </table>
    </div>
  </ng-template>
</div>
