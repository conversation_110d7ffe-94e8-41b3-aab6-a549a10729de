import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { AuditRiciPagerAlertGroupModel } from '@app/model/audit/audit-rici-pager-alert-group.model';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { NgClass, NgIf } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-audit-management-rici-pager-alert-group-detail',
  templateUrl: './audit-management-rici-pager-alert-group-detail.component.html',
  styleUrls: ['./audit-management-rici-pager-alert-group-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgIf, NgClass, TranslateModule, SharedModule],
})
export class AuditManagementRiciPagerAlertGroupDetailComponent implements OnInit, OnDestroy {
  @Input() audit: AuditRiciPagerAlertGroupModel;

  isMobile = false;
  subscriptions: Subscription[] = [];

  constructor(
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        })
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
