<div class="accordion__panel">
  <cgdis-portal-button-link (click)="showFilter = !showFilter; updateFilterNumber()" *ngIf="isMobile">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }})</span>
  </cgdis-portal-button-link>

  <!-- Mobile Filters -->
  <ng-container *ngIf="isMobile">
    <div [hidden]="!showFilter" class="row search-filter">
      <!-- Common Filters -->
      <div class="col-md-2">
        <label [translate]="'audit.actionDateTime'" class="form-label"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="dateFormControl" [datatableService]="auditPagerAlertGroupService" [filterName]="'actionDate'" [initialValue]="dateFormControl.value"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.actionType.title'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [datatableService]="auditPagerAlertGroupService" [filterName]="'actionType'" [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </div>
      <!-- Specific Filters -->
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.person.name'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="personNameFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="personNameFilterConfig" [filterName]="'personName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.person.cgdis.registration.number'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="personCgdisRegNumFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="personCgdisRegNumFilterConfig" [filterName]="'personCgdisRegistrationNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.alert.group.name'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="alertGroupNameFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="alertGroupNameFilterConfig" [filterName]="'alertGroupName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.schema.alias'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="schemaAliasFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="schemaAliasFilterConfig" [filterName]="'riciRicSchemaAlias'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.range.name'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="rangeNameFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="rangeNameFilterConfig" [filterName]="'riciRicRangeName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.pager.id'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="pagerIdFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="pagerIdFilterConfig" [filterName]="'pagerPagerId'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2"><label [translate]="'audit.rici.pager.alert.group.pager.serial.number'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="pagerSerialFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="pagerSerialFilterConfig" [filterName]="'pagerSerialNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable [datatableService]="auditPagerAlertGroupService" [sorts]="[{dir:'desc',prop:'actionDatetime'}]" [showDetails]="isMobile">
    <!-- Common Columns -->
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.actionDateTime'"></span></ng-template><ng-template epDatatableCell let-context>{{ cast(context.row).actionDatetime | dateTimeFormat }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-datepicker-filter [customFormControl]="dateFormControl" [datatableService]="auditPagerAlertGroupService" [filterName]="'actionDate'" [initialValue]="dateFormControl.value"></cgdis-portal-datatable-datepicker-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.lastName'" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.person'"></span></ng-template><ng-template epDatatableCell let-context><span class="text-wrap">{{ cast(context.row).personTecid.lastName }} {{ cast(context.row).personTecid.firstName }}</span></ng-template></ep-datatable-column>
    <ep-datatable-column [columnName]="'actionType'" [flexGrow]="1"><ng-template epDatatableHeader><span [translate]="'audit.actionType.title'"></span></ng-template><ng-template epDatatableCell let-context><span class="text-wrap">{{ 'audit.actionType.' + cast(context.row).actionType | translate }}</span></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-select-filter [allowClear]="true" [datatableService]="auditPagerAlertGroupService" [filterName]="'actionType'" [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter></ng-template>
    </ep-datatable-column>

    <!-- Pager Alert Group Columns -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personName'" [sortable]="true" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.person.name'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).personName | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="personNameFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="personNameFilterConfig" [filterName]="'personName'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personCgdisRegistrationNumber'" [sortable]="true" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.person.cgdis.registration.number'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).personCgdisRegistrationNumber | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="personCgdisRegNumFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="personCgdisRegNumFilterConfig" [filterName]="'personCgdisRegistrationNumber'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'alertGroupName'" [sortable]="true" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.alert.group.name'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).alertGroupName | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="alertGroupNameFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="alertGroupNameFilterConfig" [filterName]="'alertGroupName'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'riciRicSchemaAlias'" [sortable]="true" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.schema.alias'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).riciRicSchemaAlias | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="schemaAliasFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="schemaAliasFilterConfig" [filterName]="'riciRicSchemaAlias'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'riciRicRangeName'" [sortable]="true" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.range.name'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).riciRicRangeName | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="rangeNameFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="rangeNameFilterConfig" [filterName]="'riciRicRangeName'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'pagerPagerId'" [sortable]="true" [flexGrow]="1"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.pager.id'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).pagerPagerId | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="pagerIdFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="pagerIdFilterConfig" [filterName]="'pagerPagerId'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'pagerSerialNumber'" [sortable]="true" [flexGrow]="2"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.pager.serial.number'"></span></ng-template><ng-template epDatatableCell let-context><div class="text-wrap">{{ cast(context.row).pagerSerialNumber | defaultValue }}</div></ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter><cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="pagerSerialFormControl" [datatableService]="auditPagerAlertGroupService" [filterConfig]="pagerSerialFilterConfig" [filterName]="'pagerSerialNumber'"></cgdis-portal-datatable-text-with-null-filter></ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'alertGroupValue'" [flexGrow]="1"><ng-template epDatatableHeader><span [translate]="'audit.rici.pager.alert.group.action'"></span></ng-template><ng-template epDatatableCell let-context><span class="badge" [ngClass]="cast(context.row).alertGroupValue ? 'badge-success' : 'badge-danger'">{{ (cast(context.row).alertGroupValue ? 'audit.rici.pager.alert.group.assigned' : 'audit.rici.pager.alert.group.removed') | translate }}</span></ng-template></ep-datatable-column>

    <!-- Detail Template for Mobile -->
    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-pager-alert-group-detail [audit]="row"></cgdis-portal-audit-management-rici-pager-alert-group-detail>
      </div>
    </ng-template>
  </cgdis-portal-cgdisdatatable>
</div>
