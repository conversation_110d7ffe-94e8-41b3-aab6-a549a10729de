import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { AuditRiciPagerAlertGroupModel } from '@app/model/audit/audit-rici-pager-alert-group.model';
import { AuditManagementListRiciPagerAlertGroupService } from './audit-management-list-rici-pager-alert-group.service';
import { Subscription } from 'rxjs';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementRiciPagerAlertGroupDetailComponent } from '../detail/pager-alert-group/audit-management-rici-pager-alert-group-detail.component';
import { EpDatatableModule, FilterConfig, SearchOperator } from '@eportal/components';
import { ActivatedRoute } from '@angular/router';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-pager-alert-group',
  templateUrl:
    './audit-management-list-rici-pager-alert-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciPagerAlertGroupService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciPagerAlertGroupDetailComponent,
  ],
})
export class AuditManagementListRiciPagerAlertGroupComponent
  implements OnInit, OnDestroy
{
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;

  showFilter = false;
  isMobile = false;
  numberOfFilters = 0;
  subscriptions: Subscription[] = [];

  personNameFormControl = new FormControl<string>(undefined);
  personNameFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  personCgdisRegNumFormControl = new FormControl<string>(undefined);
  personCgdisRegNumFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  alertGroupNameFormControl = new FormControl<string>(undefined);
  alertGroupNameFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  schemaAliasFormControl = new FormControl<string>(undefined);
  schemaAliasFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  rangeNameFormControl = new FormControl<string>(undefined);
  rangeNameFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  pagerIdFormControl = new FormControl<string>(undefined);
  pagerIdFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  pagerSerialFormControl = new FormControl<string>(undefined);
  pagerSerialFilterConfig = new FilterConfig({ operator: SearchOperator.like });

  constructor(
    public auditPagerAlertGroupService: AuditManagementListRiciPagerAlertGroupService,
    private route: ActivatedRoute,
    private auditManagementService: AuditManagementService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.auditPagerAlertGroupService.addFilterWithFormControl('personName', this.personNameFormControl, this.personNameFilterConfig);
    this.auditPagerAlertGroupService.addFilterWithFormControl('personCgdisRegistrationNumber', this.personCgdisRegNumFormControl, this.personCgdisRegNumFilterConfig);
    this.auditPagerAlertGroupService.addFilterWithFormControl('alertGroupName', this.alertGroupNameFormControl, this.alertGroupNameFilterConfig);
    this.auditPagerAlertGroupService.addFilterWithFormControl('riciRicSchemaAlias', this.schemaAliasFormControl, this.schemaAliasFilterConfig);
    this.auditPagerAlertGroupService.addFilterWithFormControl('riciRicRangeName', this.rangeNameFormControl, this.rangeNameFilterConfig);
    this.auditPagerAlertGroupService.addFilterWithFormControl('pagerPagerId', this.pagerIdFormControl, this.pagerIdFilterConfig);
    this.auditPagerAlertGroupService.addFilterWithFormControl('pagerSerialNumber', this.pagerSerialFormControl, this.pagerSerialFilterConfig);

    this.subscriptions.push(
      this.auditPagerAlertGroupService.canExecuteFirstSearch().subscribe(() => {
        this.updateFilterNumber();
        this.auditPagerAlertGroupService.search();
      }),
    );
  }

  cast(row: any): AuditRiciPagerAlertGroupModel {
    return row as AuditRiciPagerAlertGroupModel;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditPagerAlertGroupService.getNumberOfFilters();
  }
}
