import { Audit } from './audit.model';

export class AuditRiciPagerAlertGroupModel extends Audit {
  personName: string;
  personCgdisRegistrationNumber: string;
  alertGroupTecid: number;
  alertGroupName: string;
  alertGroupDescription: string;
  riciRicSchemaTecid: number;
  riciRicSchemaAlias: string;
  riciRicRangeTecid: number;
  riciRicRangeName: string;
  alertGroupValue: boolean;
  pagerTecid: number;
  pagerPagerId: string;
  pagerSerialNumber: string;

  constructor(args: AuditRiciPagerAlertGroupModel) {
    super(args);
    this.personName = args.personName;
    this.personCgdisRegistrationNumber = args.personCgdisRegistrationNumber;
    this.alertGroupTecid = args.alertGroupTecid;
    this.alertGroupName = args.alertGroupName;
    this.alertGroupDescription = args.alertGroupDescription;
    this.riciRicSchemaTecid = args.riciRicSchemaTecid;
    this.riciRicSchemaAlias = args.riciRicSchemaAlias;
    this.riciRicRangeTecid = args.riciRicRangeTecid;
    this.riciRicRangeName = args.riciRicRangeName;
    this.alertGroupValue = args.alertGroupValue;
    this.pagerTecid = args.pagerTecid;
    this.pagerPagerId = args.pagerPagerId;
    this.pagerSerialNumber = args.pagerSerialNumber;
  }
}
