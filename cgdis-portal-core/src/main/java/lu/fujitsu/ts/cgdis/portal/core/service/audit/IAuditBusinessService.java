package lu.fujitsu.ts.cgdis.portal.core.service.audit;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.*;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Collection;
import java.util.List;

/**
 * The interface Audit business service.
 */
public interface IAuditBusinessService {

    /**
     * Search audit.
     *
     * @param security_constraint the security constraint
     * @param types               the types
     * @param criteria            the criteria
     * @param pageRequest
     * @return the audit
     */
    Page<Audit> search(IAuditSecurityConstraint security_constraint, Collection<AuditType> types, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditPrestation.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditPrestation
     */
    Page<AuditPrestation> searchPrestations(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditCopyPrestation.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditCopyPrestation
     */
    Page<AuditCopyPrestation> searchCopyPrestations(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditTimeSlot.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditTimeSlot
     */
    Page<AuditTimeSlot> searchTimeSlots(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditPermDeploymentPlan.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditPermDeploymentPlan
     */
    Page<AuditPermDeploymentPlan> searchPermDeploymentPlan(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);


    /**
     * Search for AuditPermServicePlanCategory.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditPermServicePlanCategory
     */
    Page<AuditPermServicePlanCategory> searchPermServicePlanCategory(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditPermConfigDpce.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditPermConfigDpce
     */
    Page<AuditPermConfigDpce> searchPermConfigDpce(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditPermConfigDpceCopy.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditPermConfigDpceCopy
     */
    Page<AuditPermConfigDpceCopy> searchPermConfigDpceCopy(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditRiciSimCard.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditRiciSimCard
     */
    Page<AuditRiciSimCard> searchRiciSimCards(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditRiciSimCardImport.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditRiciSimCardImport
     */
    Page<AuditRiciSimCardImport> searchRiciSimCardImports(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for AuditRiciPagerImport.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditRiciPagerImport
     */
    Page<AuditRiciPagerImport> searchRiciPagerImports(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Search for Audit Alert Group.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditPermConfigDpceCopy
     */
    Page<AuditRiciAlertGroup> searchAlertGroup(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);


    /**
     * Search for Audit RiciPager.
     *
     * @param security_constraint the security constraint
     * @param criteria            the search criteria
     * @param pageRequest         the page request
     * @return the page of AuditRiciPager
     */
    Page<AuditRiciPager> searchRiciPager(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest);

    Page<AuditRiciPagerAlertGroup> searchPagerAlertGroups(IAuditSecurityConstraint securityConstraint, List<AuditType> auditTypes, List<SearchCriterion> searchCriteria, PageRequest pageRequest);


}
