package lu.fujitsu.ts.cgdis.portal.connector.portaldb.converters;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.Audit;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciSimCardImport; // Import new Model
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciPagerImport; // Import new Model
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Audit dbo 2 model converter.
 */
@Service
public class AuditModel2DboConverter extends EPortalConverter<Audit, AuditDbo> {

    /**
     * The Model class.
     */
    private Map<AuditType, Class<? extends AuditDbo>> modelClass;

    /**
     * The Mapper.
     */
    private Mapper theMapper;

    /**
     * Instantiates a new Audit dbo 2 model converter.
     *
     * @param mapper the mapper
     */
    @Autowired
    @Lazy
    public AuditModel2DboConverter(Mapper mapper) {
        super(Audit.class, AuditDbo.class, mapper);
        this.theMapper = mapper;
        modelClass = new HashMap<>();
        modelClass.put(AuditType.PRESTATION, AuditPrestationDbo.class);
        modelClass.put(AuditType.PDS, AuditServicePlanDbo.class);
        modelClass.put(AuditType.MODEL, AuditModelDbo.class);
        modelClass.put(AuditType.VERSION_PDS, AuditServicePlanVersionDbo.class);
        modelClass.put(AuditType.VERSION_MODEL, AuditModelVersionDbo.class);
        modelClass.put(AuditType.COPY_PRESTATION, AuditCopyPrestationDbo.class);
        modelClass.put(AuditType.SLOT, AuditTimeSlotDbo.class);
        modelClass.put(AuditType.LOGAS, AuditLogasDbo.class);
        modelClass.put(AuditType.ALLOWANCE_CONFIG, AuditAllowanceConfigurationDbo.class);
        modelClass.put(AuditType.PERM_DEPLOYMENT_PLAN, AuditPermDeploymentPlanDbo.class);
        modelClass.put(AuditType.PERM_SERVICE_PLAN, AuditPermServicePlanCategoryDbo.class);
        modelClass.put(AuditType.PERM_CONFIG_DPCE, AuditPermConfigDpceDbo.class);
        modelClass.put(AuditType.PERM_CONFIGDPCE_COPY, AuditPermConfigDpceCopyDbo.class);
        modelClass.put(AuditType.SECURITY_ROLE, AuditSecurityRoleDbo.class);
        modelClass.put(AuditType.RICI_SIM_CARD, AuditRiciSimCardDbo.class);
        modelClass.put(AuditType.RICI_RIC_RANGE, AuditRiciRicRangeDbo.class);
        modelClass.put(AuditType.RICI_PAGER, AuditRiciPagerDbo.class);
        modelClass.put(AuditType.RICI_ALERT_GROUP, AuditRiciAlertGroupDbo.class);
        modelClass.put(AuditType.RICI_SCHEMA, AuditRiciSchemaDbo.class); // Add RICI_SCHEMA mapping
        // Add new import audit types
        modelClass.put(AuditType.RICI_SIM_CARD_IMPORT, AuditRiciSimCardImportDbo.class);
        modelClass.put(AuditType.RICI_PAGER_IMPORT, AuditRiciPagerImportDbo.class);
        modelClass.put(AuditType.RICI_PAGER_ALERT_GROUP, AuditRiciPagerAlertGroupDbo.class);
    }

    @Override
    public AuditDbo convert(Audit audit) {
        Class<? extends AuditDbo> destinationClass = modelClass.get(audit.getType());
        if (destinationClass == null) {
            throw new CGDISTechnicalException("Please provide the target DBO class mapping for the audit type: " + audit.getType());
        }
        return theMapper.map(audit, destinationClass);
    }
}
