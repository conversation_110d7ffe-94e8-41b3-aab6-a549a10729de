package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciPagerAlertGroupDbo;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AuditRiciPagerAlertGroupSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditRiciPagerAlertGroupDbo> {
    public AuditRiciPagerAlertGroupSpecificationBuilder() {
        super();
        addBuilder("personName", this::personNameSpecification);
        addBuilder("personCgdisRegistrationNumber", this::personCgdisRegistrationNumberSpecification);
        addBuilder("alertGroupName", this::alertGroupNameSpecification);
        addBuilder("riciRicSchemaAlias", this::riciRicSchemaAliasSpecification);
        addBuilder("riciRicRangeName", this::riciRicRangeNameSpecification);
        addBuilder("pagerPagerId", this::pagerPagerIdSpecification);
        addBuilder("pagerSerialNumber", this::pagerSerialNumberSpecification);
    }

    @Override
    public Specification<AuditRiciPagerAlertGroupDbo> buildSecurityConstraint(IAuditSecurityConstraint iAuditSecurityConstraint) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

    private Specification<AuditRiciPagerAlertGroupDbo> personNameSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.PERSON_NAME);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> personCgdisRegistrationNumberSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.PERSON_CGDIS_REGISTRATION_NUMBER);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> alertGroupNameSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.ALERT_GROUP_NAME);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> riciRicSchemaAliasSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.RICI_RIC_SCHEMA_ALIAS);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> riciRicRangeNameSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.RICI_RIC_RANGE_NAME);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> pagerPagerIdSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.PAGER_PAGER_ID);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> pagerSerialNumberSpecification(SearchCriterion criterion) {
        return riciPagerAlertGroupSpecification(criterion, AuditRiciPagerAlertGroupDbo.Fields.PAGER_SERIAL_NUMBER);
    }

    private Specification<AuditRiciPagerAlertGroupDbo> riciPagerAlertGroupSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            return SpecificationUtils.buildPredicate(
                    root.get(fieldName), criterion, criteriaBuilder
            );
        };
    }
}
