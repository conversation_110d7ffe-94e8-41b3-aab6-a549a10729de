package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciPagerAlertGroupDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.AbstractRepositoryImpl;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Repository
public class IAuditRiciPagerAlertGroupRepositoryImpl extends AbstractRepositoryImpl<AuditRiciPagerAlertGroupDbo> implements IAuditRiciPagerAlertGroupRepositoryCustom {
    @PersistenceContext
    private EntityManager entityManager;

    public IAuditRiciPagerAlertGroupRepositoryImpl() {
        super(AuditRiciPagerAlertGroupDbo.class);
    }

    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }
}
