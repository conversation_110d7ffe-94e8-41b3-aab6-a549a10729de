package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciPagerAlertGroupDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IBaseCGDISRepository;

/**
 * Repository interface for RICI Pager Alert Group audit records.
 */
public interface IAuditRiciPagerAlertGroupRepository extends IBaseCGDISRepository<AuditRiciPagerAlertGroupDbo>, IAuditRiciPagerAlertGroupRepositoryCustom {

}
